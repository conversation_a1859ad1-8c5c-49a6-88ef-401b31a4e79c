#include "stm32f10x.h"
#include <stdio.h>
#include <stdarg.h>

uint8_t USART_RxData;
uint8_t USART_RxFlag;

/**
  * @brief  串口初始化
  * @param  无
  * @retval 无
  */
void USART1_Init(void)
{
	/*开启时钟*/
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART1, ENABLE);	//开启USART1的时钟
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);	//开启GPIOA的时钟

	/*GPIO初始化*/
	GPIO_InitTypeDef GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOA, &GPIO_InitStructure);					//将PA9引脚初始化为复用推挽输出

	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOA, &GPIO_InitStructure);					//将PA10引脚初始化为上拉输入

	/*USART初始化*/
	USART_InitTypeDef USART_InitStructure;					//定义结构体变量
	USART_InitStructure.USART_BaudRate = 9600;				//波特率
	USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;	//硬件流控制，不需要
	USART_InitStructure.USART_Mode = USART_Mode_Tx | USART_Mode_Rx;	//模式，发送模式和接收模式均选择
	USART_InitStructure.USART_Parity = USART_Parity_No;		//奇偶校验，不需要
	USART_InitStructure.USART_StopBits = USART_StopBits_1;		//停止位，选择1位
	USART_InitStructure.USART_WordLength = USART_WordLength_8b;	//字长，选择8位
	USART_Init(USART1, &USART_InitStructure);				//将结构体变量交给USART_Init，配置USART1

	/*中断输出配置*/
	USART_ITConfig(USART1, USART_IT_RXNE, ENABLE);			//开启串口接收数据的中断

	/*NVIC中断分组*/
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);			//配置NVIC为分组2

	/*NVIC配置*/
	NVIC_InitTypeDef NVIC_InitStructure;					//定义结构体变量
	NVIC_InitStructure.NVIC_IRQChannel = USART1_IRQn;		//选择配置NVIC的USART1线
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;			//指定NVIC线路使能
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;		//指定NVIC线路的抢占优先级为1
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;		//指定NVIC线路的响应优先级为1
	NVIC_Init(&NVIC_InitStructure);							//将结构体变量交给NVIC_Init，配置NVIC外设

	/*USART使能*/
	USART_Cmd(USART1, ENABLE);								//使能USART1，串口开始运行
}

/**
  * @brief  串口发送一个字节
  * @param  Byte 要发送的一个字节
  * @retval 无
  */
void USART_SendByte(uint8_t Byte)
{
	USART_SendData(USART1, Byte);		//将字节数据写入数据寄存器，写入后USART自动生成时序波形
	while (USART_GetFlagStatus(USART1, USART_FLAG_TXE) == RESET);	//等待发送完成
	/*下次写入数据寄存器会自动清除发送完成标志位，故此循环后，无需清除标志位*/
}

/**
  * @brief  串口发送一个数组
  * @param  Array 要发送数组的首地址
  * @param  Length 要发送数组的长度
  * @retval 无
  */
void USART_SendArray(uint8_t *Array, uint16_t Length)
{
	uint16_t i;
	for (i = 0; i < Length; i ++)		//遍历数组
	{
		USART_SendByte(Array[i]);		//依次调用USART_SendByte发送每个字节数据
	}
}

/**
  * @brief  串口发送一个字符串
  * @param  String 要发送字符串的首地址
  * @retval 无
  */
void USART_SendString(char *String)
{
	uint8_t i;
	for (i = 0; String[i] != '\0'; i ++)//遍历字符数组（字符串），遇到字符串结束标志位后停止
	{
		USART_SendByte(String[i]);		//依次调用USART_SendByte发送每个字节数据
	}
}

/**
  * @brief  次方函数（内部使用）
  * @retval 返回值等于X的Y次方
  */
uint32_t USART_Pow(uint32_t X, uint32_t Y)
{
	uint32_t Result = 1;	//设置结果初值为1
	while (Y --)			//执行Y次
	{
		Result *= X;		//将X累乘到结果
	}
	return Result;
}

/**
  * @brief  串口发送数字
  * @param  Number 要发送的数字，范围：0~4294967295
  * @param  Length 要发送数字的长度，范围：0~10
  * @retval 无
  */
void USART_SendNumber(uint32_t Number, uint8_t Length)
{
	uint8_t i;
	for (i = 0; i < Length; i ++)		//根据数字长度遍历数字的每一位
	{
		USART_SendByte(Number / USART_Pow(10, Length - i - 1) % 10 + '0');	//依次调用USART_SendByte发送每位数字
	}
}

/**
  * @brief  串口接收一个字节（查询接收）
  * @param  无
  * @retval 接收的一个字节数据
  */
uint8_t USART_ReceiveByte(void)
{
	while (USART_GetFlagStatus(USART1, USART_FLAG_RXNE) == RESET);	//等待接收完成
	return USART_ReceiveData(USART1);								//读取数据寄存器，获取接收到的数据
}

/**
  * @brief  获取串口接收标志位
  * @param  无
  * @retval 串口接收标志位，范围：0~1，接收到数据后，标志位置1，读取后标志位自动清零
  */
uint8_t USART_GetRxFlag(void)
{
	if (USART_RxFlag == 1)			//如果标志位为1
	{
		USART_RxFlag = 0;
		return 1;					//则返回1，并自动清零标志位
	}
	return 0;						//如果标志位为0，则返回0
}

/**
  * @brief  清除串口接收标志位
  * @param  无
  * @retval 无
  */
void USART_ClearRxFlag(void)
{
	USART_RxFlag = 0;				//清零标志位
}

/**
  * @brief  获取串口接收的数据
  * @param  无
  * @retval 接收的数据
  */
uint8_t USART_GetRxData(void)
{
	return USART_RxData;
}

/**
  * @brief  设置串口接收标志位
  * @param  无
  * @retval 无
  */
void USART_SetRxFlag(void)
{
	USART_RxFlag = 1;
}

/**
  * @brief  设置串口接收数据
  * @param  Data 接收的数据
  * @retval 无
  */
void USART_SetRxData(uint8_t Data)
{
	USART_RxData = Data;
}