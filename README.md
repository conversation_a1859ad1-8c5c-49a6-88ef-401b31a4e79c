# OpenMV黑色色块追踪系统

这个项目实现了OpenMV识别黑色色块并将数据发送给STM32，然后在OLED显示屏上显示追踪信息。

## 系统组成

### 硬件连接

#### STM32端 (接收端)
- **OLED显示屏**:
  - SCL -> PB8
  - SDA -> PB9
  - VCC -> 3.3V
  - GND -> GND

- **串口连接**:
  - USART1_TX (PA9) -> OpenMV UART3_RX
  - USART1_RX (PA10) -> OpenMV UART3_TX
  - GND -> GND

#### OpenMV端 (发送端)
- **串口连接**:
  - UART3_TX -> STM32 USART1_RX (PA10)
  - UART3_RX -> STM32 USART1_TX (PA9)
  - GND -> GND

### 软件功能

#### STM32功能
1. **串口通信**: 接收OpenMV发送的数据
2. **数据解析**: 解析色块坐标和视野中心坐标
3. **OLED显示**: 实时显示追踪信息
   - 第1行: 标题 "OpenMV Tracker"
   - 第2行: 色块X坐标 "Blob X: xxx"
   - 第3行: 视野中心 "Center: xxx"
   - 第4行: 坐标差值 "Diff: ±xxx"

#### OpenMV功能
1. **图像采集**: 320x240分辨率
2. **黑色识别**: 使用LAB颜色空间检测黑色色块
3. **数据发送**: 通过串口发送坐标信息
4. **可视化**: 在图像上绘制检测结果

## 数据格式

OpenMV发送给STM32的数据格式：
```
X:123,C:160\n
```
- `X:123`: 黑色色块中心的X坐标
- `C:160`: 视野中心的X坐标
- `\n`: 数据结束标志

当没有检测到色块时：
```
X:-1,C:160\n
```

## 使用方法

### 1. STM32端设置
1. 将STM32项目编译并下载到开发板
2. 确保OLED显示屏正确连接
3. 确保串口连接正确

### 2. OpenMV端设置
1. 将 `openmv_black_blob_tracker.py` 文件复制到OpenMV IDE
2. 连接OpenMV摄像头
3. 运行脚本

### 3. 系统运行
1. 先启动STM32程序
2. 再运行OpenMV脚本
3. 在OpenMV摄像头前放置黑色物体
4. 观察OLED显示屏上的追踪信息

## 参数调整

### OpenMV参数
- **黑色阈值**: 修改 `black_threshold` 变量来调整黑色检测的敏感度
- **最小像素数**: 修改 `pixels_threshold` 来过滤小的噪点
- **最小面积**: 修改 `area_threshold` 来设置最小检测面积

### STM32参数
- **串口波特率**: 默认9600，可在 `USART_Init()` 函数中修改
- **显示刷新率**: 修改主循环中的延时来调整刷新频率

## 故障排除

1. **OLED无显示**: 检查I2C连接和电源
2. **无串口数据**: 检查串口连接和波特率设置
3. **检测不准确**: 调整黑色阈值参数
4. **数据解析错误**: 检查数据格式和解析逻辑

## 扩展功能

可以在此基础上添加：
- PID控制算法实现自动追踪
- 多色块检测
- 色块大小信息传输
- 无线通信替代串口通信