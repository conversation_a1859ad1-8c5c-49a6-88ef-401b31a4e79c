# OpenMV测试代码 - 用于测试串口通信
import time, pyb
from pyb import UART

print("=== OpenMV测试程序启动 ===")

# 初始化串口 (UART3, 波特率9600)
try:
    uart = UART(3, 9600)
    print("串口初始化成功")
except Exception as e:
    print("串口初始化失败:", e)

print("开始发送测试数据...")

counter = 0

while(True):
    # 发送简单的测试数据
    test_x = 5 + (counter % 5)     # X坐标在5-9之间变化（单位数）
    test_c = 80                    # 固定中心坐标

    data = "X:{},C:{}\n".format(test_x, test_c)

    try:
        uart.write(data)
        print("第{}次发送: {} (长度:{})".format(counter + 1, data.strip(), len(data)))

        # 显示发送的原始字节
        data_bytes = [ord(c) for c in data]
        print("字节: {}".format(data_bytes))

    except Exception as e:
        print("发送失败:", e)

    counter += 1

    # 每3秒发送一次，便于观察
    time.sleep_ms(3000)