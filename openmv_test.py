# OpenMV测试代码 - 用于测试串口通信
import time, pyb
from pyb import UART

# 初始化串口 (UART3, 波特率9600)
uart = UART(3, 9600)

print("OpenMV测试程序启动...")
print("开始发送测试数据...")

counter = 0

while(True):
    # 发送测试数据
    test_x = 50 + (counter % 100)  # X坐标在50-149之间变化
    test_c = 160                   # 固定中心坐标

    data = "X:{},C:{}\n".format(test_x, test_c)
    uart.write(data)

    print("发送: " + data.strip())

    counter += 1
    time.sleep_ms(500)  # 每500ms发送一次数据