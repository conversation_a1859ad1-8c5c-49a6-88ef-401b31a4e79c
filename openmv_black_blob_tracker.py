# OpenMV黑色色块识别与追踪
# 识别黑色色块并将色块中心x坐标和视野中心x坐标发送给STM32

import sensor, image, time, pyb
from pyb import UART

# 初始化摄像头
sensor.reset()
sensor.set_pixformat(sensor.RGB565)    # 设置像素格式为RGB565
sensor.set_framesize(sensor.QVGA)      # 设置分辨率为QVGA (320x240)
sensor.skip_frames(time = 2000)        # 跳过前2秒的帧，让摄像头稳定
sensor.set_auto_gain(False)            # 关闭自动增益
sensor.set_auto_whitebal(False)        # 关闭自动白平衡

# 初始化串口 (UART3, 波特率9600)
uart = UART(3, 9600)

# 黑色阈值 (RGB颜色空间)
# RGB格式: (R_min, R_max, G_min, G_max, B_min, B_max)
# 黑色的RGB值都比较低，通常在0-50范围内
black_threshold = (0, 50, 0, 50, 0, 50)

# 获取图像尺寸
img_width = 320
img_height = 240
center_x = img_width // 2  # 视野中心x坐标

# LED指示灯
red_led = pyb.LED(1)
green_led = pyb.LED(2)
blue_led = pyb.LED(3)

clock = time.clock()

print("OpenMV黑色色块追踪器启动...")
print("图像尺寸: {}x{}".format(img_width, img_height))
print("视野中心x坐标: {}".format(center_x))

while(True):
    clock.tick()

    # 获取图像
    img = sensor.snapshot()

    # 直接在RGB图像上查找黑色色块
    # 查找黑色色块
    blobs = img.find_blobs([black_threshold],
                           pixels_threshold=200,    # 最小像素数
                           area_threshold=200,      # 最小面积
                           merge=True)              # 合并重叠的色块

    blob_found = False
    blob_center_x = -1

    if blobs:
        # 找到最大的色块
        largest_blob = max(blobs, key=lambda b: b.pixels())

        # 获取色块中心坐标
        blob_center_x = largest_blob.cx()
        blob_center_y = largest_blob.cy()

        # 在图像上绘制色块边界和中心点
        img.draw_rectangle(largest_blob.rect(), color=(255, 0, 0))  # 红色边界
        img.draw_cross(blob_center_x, blob_center_y, color=(0, 255, 0))  # 绿色十字

        # 绘制色块信息
        img.draw_string(10, 10, "Blob: ({},{})".format(blob_center_x, blob_center_y),
                       color=(255, 255, 255))

        blob_found = True
        green_led.on()  # 绿灯表示找到色块
        red_led.off()
    else:
        red_led.on()    # 红灯表示未找到色块
        green_led.off()

    # 绘制视野中心线
    img.draw_line(center_x, 0, center_x, img_height-1, color=(0, 0, 255))  # 蓝色中心线

    # 显示视野中心坐标
    img.draw_string(10, 30, "Center: {}".format(center_x), color=(255, 255, 255))

    # 发送数据到STM32（无论是否找到色块都发送）
    if blob_found:
        diff_x = blob_center_x - center_x
        img.draw_string(10, 50, "Diff: {}".format(diff_x), color=(255, 255, 255))

        # 发送数据到STM32
        data_string = "X:{},C:{}\n".format(blob_center_x, center_x)
        uart.write(data_string)
        print("发送: " + data_string.strip())  # 添加控制台输出

        # 蓝灯闪烁表示数据发送
        blue_led.on()
        pyb.delay(10)
        blue_led.off()
    else:
        # 没有找到色块时，发送特殊值
        data_string = "X:-1,C:{}\n".format(center_x)
        uart.write(data_string)
        print("未检测到色块，发送: " + data_string.strip())  # 添加控制台输出

    # 显示帧率
    img.draw_string(10, img_height-20, "FPS: {:.1f}".format(clock.fps()),
                   color=(255, 255, 255))

    # 短暂延时
    pyb.delay(50)