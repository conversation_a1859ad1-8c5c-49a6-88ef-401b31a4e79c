# OpenMV简化版黑色色块追踪器
# 适用于QQVGA分辨率 (160x120)

import sensor, image, time, pyb
from pyb import UART

# 初始化摄像头
sensor.reset()
sensor.set_pixformat(sensor.GRAYSCALE)  # 使用灰度图像，更简单
sensor.set_framesize(sensor.QQVGA)      # 设置分辨率为QQVGA (160x120)
sensor.skip_frames(time = 2000)         # 跳过前2秒的帧
sensor.set_auto_gain(False)             # 关闭自动增益
sensor.set_auto_whitebal(False)         # 关闭自动白平衡

# 初始化串口 (UART3, 波特率9600)
uart = UART(3, 9600)

# 图像尺寸
img_width = 160
img_height = 120
center_x = img_width // 2  # 视野中心x坐标 = 80

# 黑色阈值 (灰度值，0-255，黑色接近0)
black_threshold = [(0, 50)]  # 检测灰度值0-50的区域作为黑色

print("OpenMV简化版黑色追踪器启动...")
print("分辨率: {}x{}".format(img_width, img_height))
print("中心X: {}".format(center_x))

while(True):
    # 获取图像
    img = sensor.snapshot()

    # 查找黑色色块
    blobs = img.find_blobs(black_threshold,
                          pixels_threshold=50,   # 最小像素数
                          area_threshold=50,     # 最小面积
                          merge=True)            # 合并重叠色块

    if blobs:
        # 找到最大的色块
        largest_blob = max(blobs, key=lambda b: b.pixels())
        blob_x = largest_blob.cx()

        # 绘制色块
        img.draw_rectangle(largest_blob.rect())
        img.draw_cross(blob_x, largest_blob.cy())

        # 发送数据
        data = "X:{},C:{}\n".format(blob_x, center_x)
        uart.write(data)
        print("发送: " + data.strip())

    else:
        # 没找到色块
        data = "X:-1,C:{}\n".format(center_x)
        uart.write(data)
        print("未检测到色块")

    # 绘制中心线
    img.draw_line(center_x, 0, center_x, img_height-1)

    time.sleep_ms(100)  # 延时100ms