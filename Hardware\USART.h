#ifndef __USART_H
#define __USART_H

#include <stdio.h>

void USART_Init(void);
void USART_SendByte(uint8_t Byte);
void USART_SendArray(uint8_t *Array, uint16_t Length);
void USART_SendString(char *String);
void USART_SendNumber(uint32_t Number, uint8_t Length);
uint8_t USART_ReceiveByte(void);
uint8_t USART_GetRxFlag(void);
void USART_ClearRxFlag(void);
uint8_t USART_GetRxData(void);
void USART_SetRxFlag(void);
void USART_SetRxData(uint8_t Data);

#endif