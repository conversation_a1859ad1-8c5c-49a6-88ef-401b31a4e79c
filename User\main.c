#include "stm32f10x.h"                  // Device header
#include "Delay.h"
#include "OLED.h"
#include "USART.h"

// 全局变量
uint8_t RxData[20];		// 接收数据缓冲区
uint8_t RxIndex = 0;	// 接收数据索引
int16_t blob_x = -1;	// 黑色色块中心x坐标
int16_t center_x = 160;	// 视野中心x坐标 (QVGA分辨率320x240的中心)
int16_t diff_x = 0;		// x坐标差值
uint16_t data_count = 0; // 接收数据计数器，用于调试

/**
  * @brief  解析接收到的数据
  * @param  无
  * @retval 无
  */
void ParseReceivedData(void)
{
	// 数据格式: "X:123,C:160\n"
	// X: 黑色色块中心x坐标
	// C: 视野中心x坐标

	data_count++;  // 增加数据计数器

	if (RxIndex >= 7)  // 确保有足够的数据 (最短: X:1,C:8)
	{
		uint8_t x_found = 0, c_found = 0;

		// 查找X:
		for (uint8_t i = 0; i < RxIndex - 1; i++)
		{
			if (RxData[i] == 'X' && RxData[i+1] == ':')
			{
				// 解析X坐标
				blob_x = 0;
				i += 2;
				uint8_t is_negative = 0;

				// 检查是否为负数
				if (i < RxIndex && RxData[i] == '-')
				{
					is_negative = 1;
					i++;
				}

				// 解析数字
				while (i < RxIndex && RxData[i] >= '0' && RxData[i] <= '9')
				{
					blob_x = blob_x * 10 + (RxData[i] - '0');
					i++;
				}

				// 应用负号
				if (is_negative)
				{
					blob_x = -blob_x;
				}
				x_found = 1;
				break;
			}
		}

		// 查找C:
		for (uint8_t i = 0; i < RxIndex - 1; i++)
		{
			if (RxData[i] == 'C' && RxData[i+1] == ':')
			{
				// 解析中心坐标
				center_x = 0;
				i += 2;
				while (i < RxIndex && RxData[i] >= '0' && RxData[i] <= '9')
				{
					center_x = center_x * 10 + (RxData[i] - '0');
					i++;
				}
				c_found = 1;
				break;
			}
		}

		// 计算差值
		if (blob_x >= 0)
		{
			diff_x = blob_x - center_x;
		}
		else
		{
			diff_x = 0;  // 没有检测到色块时差值为0
		}
	}

	// 清空接收缓冲区
	RxIndex = 0;
}

int main(void)
{
	/*模块初始化*/
	OLED_Init();		//OLED初始化
	USART1_Init();		//串口初始化

	/*OLED显示标题*/
	OLED_Clear();
	OLED_ShowString(1, 1, "OpenMV Tracker");
	OLED_ShowString(2, 1, "Blob X:");
	OLED_ShowString(3, 1, "Center:");
	OLED_ShowString(4, 1, "Count :");

	while (1)
	{
		// 检查是否有串口数据接收
		if (USART_GetRxFlag())
		{
			uint8_t receivedByte = USART_GetRxData();

			// 如果接收到换行符，表示一帧数据结束
			if (receivedByte == '\n' || receivedByte == '\r')
			{
				ParseReceivedData();
			}
			else if (RxIndex < 19)  // 防止缓冲区溢出
			{
				RxData[RxIndex++] = receivedByte;
			}
		}

		// 更新OLED显示
		if (blob_x >= 0)
		{
			OLED_ShowNum(2, 8, blob_x, 3);		// 显示色块x坐标
		}
		else
		{
			OLED_ShowString(2, 8, "---");		// 没有检测到色块
		}

		OLED_ShowNum(3, 8, center_x, 3);		// 显示中心x坐标
		OLED_ShowNum(4, 8, data_count, 4);		// 显示接收数据计数

		Delay_ms(50);  // 延时50ms，避免刷新过快
	}
}
