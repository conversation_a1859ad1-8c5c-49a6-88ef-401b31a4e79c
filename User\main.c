#include "stm32f10x.h"                  // Device header
#include "Delay.h"
#include "OLED.h"
#include "USART.h"

// 全局变量
uint8_t RxData[20];		// 接收数据缓冲区
uint8_t RxIndex = 0;	// 接收数据索引
int16_t blob_x = -1;	// 黑色色块中心x坐标
int16_t center_x = 0;	// 视野中心x坐标
int16_t diff_x = 0;		// x坐标差值
uint16_t data_count = 0; // 接收数据计数器，用于调试
uint8_t last_data_len = 0; // 最后一次接收的数据长度
uint8_t first_char = 0;    // 第一个字符，用于调试

/**
  * @brief  解析接收到的数据
  * @param  无
  * @retval 无
  */
void ParseReceivedData(void)
{
	// 数据格式: "X:11,C:80\n"

	data_count++;  // 增加数据计数器
	last_data_len = RxIndex;  // 记录数据长度

	// 记录第一个字符用于调试
	if (RxIndex > 0)
	{
		first_char = RxData[0];
	}

	// 确保有足够的数据
	if (RxIndex < 7)
	{
		RxIndex = 0;
		return;
	}

	// 添加字符串结束符
	if (RxIndex < 19)
	{
		RxData[RxIndex] = '\0';
	}

	// 重置值
	int16_t temp_x = -1;
	int16_t temp_c = 0;

	// 查找并解析X值
	for (uint8_t i = 0; i <= RxIndex - 2; i++)
	{
		if (RxData[i] == 'X' && RxData[i+1] == ':')
		{
			i += 2; // 跳过"X:"
			temp_x = 0;
			uint8_t is_negative = 0;

			// 检查负号
			if (i < RxIndex && RxData[i] == '-')
			{
				is_negative = 1;
				i++;
			}

			// 解析数字
			while (i < RxIndex && RxData[i] >= '0' && RxData[i] <= '9')
			{
				temp_x = temp_x * 10 + (RxData[i] - '0');
				i++;
			}

			if (is_negative)
			{
				temp_x = -temp_x;
			}
			break;
		}
	}

	// 查找并解析C值
	for (uint8_t i = 0; i <= RxIndex - 2; i++)
	{
		if (RxData[i] == 'C' && RxData[i+1] == ':')
		{
			i += 2; // 跳过"C:"
			temp_c = 0;

			// 解析数字
			while (i < RxIndex && RxData[i] >= '0' && RxData[i] <= '9')
			{
				temp_c = temp_c * 10 + (RxData[i] - '0');
				i++;
			}
			break;
		}
	}

	// 只有成功解析才更新全局变量
	blob_x = temp_x;
	if (temp_c > 0)
	{
		center_x = temp_c;
	}

	// 计算差值
	if (blob_x >= 0)
	{
		diff_x = blob_x - center_x;
	}
	else
	{
		diff_x = 0;
	}

	// 清空接收缓冲区
	RxIndex = 0;
}

int main(void)
{
	/*模块初始化*/
	OLED_Init();		//OLED初始化
	USART1_Init();		//串口初始化

	/*OLED显示标题*/
	OLED_Clear();
	OLED_ShowString(1, 1, "OpenMV Debug");
	OLED_ShowString(2, 1, "X:");
	OLED_ShowString(3, 1, "C:");
	OLED_ShowString(4, 1, "1st:");

	while (1)
	{
		// 检查是否有串口数据接收
		if (USART_GetRxFlag())
		{
			uint8_t receivedByte = USART_GetRxData();

			// 如果接收到换行符，表示一帧数据结束
			if (receivedByte == '\n' || receivedByte == '\r')
			{
				ParseReceivedData();
			}
			else if (RxIndex < 19)  // 防止缓冲区溢出
			{
				RxData[RxIndex++] = receivedByte;
			}
		}

		// 更新OLED显示
		if (blob_x >= 0)
		{
			OLED_ShowSignedNum(2, 3, blob_x, 4);	// 显示色块x坐标（带符号）
		}
		else
		{
			OLED_ShowString(2, 3, " ---");			// 没有检测到色块
		}

		OLED_ShowNum(3, 3, center_x, 4);			// 显示中心x坐标
		OLED_ShowNum(4, 5, first_char, 3);			// 显示第一个字符的ASCII码

		Delay_ms(50);  // 延时50ms，避免刷新过快
	}
}
